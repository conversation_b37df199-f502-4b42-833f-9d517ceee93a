# خاصية اللغة - Language Feature

## نظرة عامة / Overview

تم إضافة خاصية دعم اللغات المتعددة للموقع مع دعم ثلاث لغات:
- العربية (Arabic) - RTL
- الإنجليزية (English) - LTR  
- الفرنسية (French) - LTR

## الميزات المضافة / Added Features

### 1. مفتاح تبديل اللغة / Language Selector
- موقع ثابت في أعلى يمين الصفحة (أعلى يسار للعربية)
- تصميم جذاب مع تأثيرات بصرية
- أعلام الدول لكل لغة
- حفظ اللغة المختارة في localStorage

### 2. نظام الترجمة / Translation System
- ملف JavaScript منفصل لإدارة الترجمات
- دعم خصائص data-translate للعناصر
- ترجمة تلقائية عند تغيير اللغة
- دعم اتجاه النص (RTL/LTR)

### 3. دعم CSS محسن / Enhanced CSS Support
- خطوط مختلفة لكل لغة (Cairo للعربية، Inter للإنجليزية والفرنسية)
- دعم شامل لاتجاه النص
- تحسينات للنماذج والجداول والبطاقات
- تصميم متجاوب للشاشات المختلفة

## كيفية الاستخدام / How to Use

### للمطورين / For Developers

#### إضافة نص قابل للترجمة:
```html
<h1 data-translate="site_title">نظام إدارة المراكز الصحية</h1>
<button data-translate="save">حفظ</button>
```

#### إضافة ترجمة جديدة في JavaScript:
```javascript
// في ملف js/language-manager.js
this.translations = {
    ar: {
        'new_key': 'النص العربي'
    },
    en: {
        'new_key': 'English Text'
    },
    fr: {
        'new_key': 'Texte Français'
    }
};
```

#### استخدام دالة الترجمة في JavaScript:
```javascript
const translatedText = t('key_name');
// أو
const translatedText = window.languageManager.translate('key_name');
```

### للمستخدمين / For Users

1. **تغيير اللغة**: انقر على أحد أزرار اللغة في أعلى الصفحة
2. **حفظ تلقائي**: اللغة المختارة تُحفظ تلقائياً وتُطبق عند العودة للموقع
3. **تغيير اتجاه النص**: يتغير اتجاه النص تلقائياً حسب اللغة المختارة

## الملفات المعدلة / Modified Files

1. **js/language-manager.js** - ملف جديد لإدارة اللغات
2. **index.php** - إضافة خصائص data-translate ومرجع للملف
3. **cs-manager.html** - إضافة خصائص data-translate ومرجع للملف

## الترجمات المتوفرة / Available Translations

### الصفحة الرئيسية / Main Page
- عنوان الموقع
- وصف الموقع  
- حالة النظام
- المميزات الرئيسية
- الأزرار والروابط
- معلومات الإصدار

### واجهة إدارة المراكز الصحية / Healthcare Management Interface
- القائمة الجانبية
- نماذج تسجيل الدخول والتسجيل
- أسماء الصفحات والأقسام
- الأزرار الأساسية (حفظ، حذف، إلغاء، إلخ)

## التحسينات المستقبلية / Future Improvements

1. إضافة المزيد من الترجمات للصفحات الفرعية
2. دعم لغات إضافية
3. ترجمة الرسائل والإشعارات
4. ترجمة محتوى قواعد البيانات
5. إضافة خيار تحديد اللغة في إعدادات المستخدم

## الدعم الفني / Technical Support

- تأكد من تحميل ملف `js/language-manager.js` قبل استخدام الترجمات
- استخدم خصائص `data-translate` للعناصر القابلة للترجمة
- تأكد من إضافة الترجمات لجميع اللغات المدعومة
- اختبر التصميم في جميع اللغات للتأكد من التوافق

---

تم تطوير هذه الخاصية لتحسين تجربة المستخدم ودعم المستخدمين متعددي اللغات.
This feature was developed to enhance user experience and support multilingual users.
Cette fonctionnalité a été développée pour améliorer l'expérience utilisateur et prendre en charge les utilisateurs multilingues.
