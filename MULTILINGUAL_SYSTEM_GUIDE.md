# دليل النظام متعدد اللغات - Multilingual System Guide

## 🌐 نظرة عامة / Overview

تم تطوير نظام شامل متعدد اللغات لموقع إدارة المراكز الصحية يدعم ثلاث لغات:
- **العربية** 🇸🇦 (RTL) - الافتراضية
- **الإنجليزية** 🇺🇸 (LTR)
- **الفرنسية** 🇫🇷 (LTR)

---

## 📁 هيكل الملفات / File Structure

### الصفحات الرئيسية / Main Pages
```
├── index.php          # الصفحة الرئيسية العربية
├── index-en.php       # الصفحة الرئيسية الإنجليزية  
├── index-fr.php       # الصفحة الرئيسية الفرنسية
├── cs-manager.html    # واجهة إدارة المراكز (متعددة اللغات)
```

### ملفات الترجمة / Translation Files
```
├── translations/
│   ├── ar.json        # الترجمات العربية
│   ├── en.json        # الترجمات الإنجليزية
│   └── fr.json        # الترجمات الفرنسية
```

### ملفات النظام / System Files
```
├── js/
│   └── language-manager.js    # مدير اللغات الرئيسي
├── test-translation.html      # صفحة اختبار الترجمات
└── test-language.html         # صفحة اختبار النظام
```

---

## 🎯 كيفية الاستخدام / How to Use

### للمستخدمين / For Users

1. **الوصول للموقع**:
   - افتح `index.php` (العربية افتراضياً)
   - أو `index-en.php` للإنجليزية مباشرة
   - أو `index-fr.php` للفرنسية مباشرة

2. **تغيير اللغة**:
   - انقر على أزرار اللغة في أعلى الصفحة
   - 🇸🇦 العربية | 🇺🇸 English | 🇫🇷 Français
   - سيتم التوجيه تلقائياً للصفحة المناسبة

3. **الحفظ التلقائي**:
   - اختيارك محفوظ في `localStorage`
   - عند العودة للموقع ستجد اللغة كما تركتها

### للمطورين / For Developers

#### إضافة نص قابل للترجمة:
```html
<!-- استخدم data-translate مع المفتاح المناسب -->
<h1 data-translate="main_page_site_title">النص العربي</h1>
<button data-translate="common_save">حفظ</button>
```

#### إضافة ترجمة جديدة:
```json
// في translations/ar.json
{
  "common": {
    "new_key": "النص العربي الجديد"
  }
}

// في translations/en.json  
{
  "common": {
    "new_key": "New English Text"
  }
}

// في translations/fr.json
{
  "common": {
    "new_key": "Nouveau Texte Français"
  }
}
```

#### استخدام الترجمة في JavaScript:
```javascript
// باستخدام مدير اللغات
const text = window.languageManager.translate('common_save');

// أو باستخدام الدالة المساعدة
const text = t('common_save');
```

---

## 🔧 المكونات التقنية / Technical Components

### 1. مدير اللغات (language-manager.js)
- **تحميل الترجمات**: من ملفات JSON أو احتياطية مدمجة
- **إدارة اللغة**: حفظ واستعادة اللغة المختارة
- **تحديث الواجهة**: تطبيق الترجمات وتغيير اتجاه النص
- **مفتاح اللغة**: إنشاء وإدارة أزرار تبديل اللغة

### 2. ملفات الترجمة (JSON)
- **هيكل منظم**: مقسمة حسب الأقسام (common, main_page, healthcare_management, إلخ)
- **مفاتيح موحدة**: نفس المفاتيح في جميع اللغات
- **تحميل تلقائي**: يتم تحميلها عند بدء النظام

### 3. الصفحات المتخصصة
- **صفحة لكل لغة**: محتوى مترجم بالكامل
- **توجيه ذكي**: انتقال تلقائي حسب اللغة المحفوظة
- **تصميم متجاوب**: يعمل على جميع الأجهزة

---

## 📊 الترجمات المتوفرة / Available Translations

### الأقسام المترجمة / Translated Sections:

#### 1. العناصر المشتركة / Common Elements
- أزرار الإجراءات (حفظ، إلغاء، حذف، إلخ)
- رسائل النظام (تحميل، خطأ، نجح، إلخ)
- عناصر التنقل الأساسية

#### 2. الصفحة الرئيسية / Main Page
- عنوان ووصف الموقع
- حالة النظام والتثبيت
- المميزات الرئيسية
- أزرار الإجراءات
- معلومات الإصدار

#### 3. واجهة إدارة المراكز الصحية / Healthcare Management
- القائمة الجانبية
- نماذج تسجيل الدخول والتسجيل
- أسماء الأقسام والصفحات
- عناصر التحكم الأساسية

#### 4. إدارة التلقيحات / Vaccination Management
- معلومات الأطفال
- جداول التلقيحات
- أسماء اللقاحات
- حالات التلقيح

#### 5. إدارة الأدوية / Medicine Management
- معلومات الأدوية
- إدارة المخزون
- فئات الأدوية
- تعليمات الاستخدام

#### 6. تنظيم الأسرة / Family Planning
- وسائل منع الحمل
- معلومات العملاء
- المتابعة والاستشارة
- الآثار الجانبية

---

## 🎨 التصميم والتخطيط / Design & Layout

### دعم اتجاه النص / Text Direction Support
- **RTL للعربية**: اتجاه من اليمين لليسار
- **LTR للإنجليزية والفرنسية**: اتجاه من اليسار لليمين
- **تحديث تلقائي**: للخطوط والتخطيط

### الخطوط / Fonts
- **العربية**: Cairo font family
- **الإنجليزية والفرنسية**: Inter font family
- **احتياطية**: Segoe UI, Tahoma, Geneva, Verdana

### مفتاح اللغة / Language Selector
- **موقع ثابت**: أعلى يمين الصفحة (أعلى يسار للعربية)
- **تصميم شفاف**: خلفية ضبابية مع تأثيرات بصرية
- **تفاعلي**: تأثيرات hover وانتقالات سلسة

---

## 🔍 اختبار النظام / System Testing

### ملفات الاختبار / Test Files
1. **test-translation.html**: اختبار الترجمات الأساسية
2. **test-language.html**: اختبار شامل للنظام
3. **وحدة التحكم**: رسائل تشخيص مفصلة

### خطوات الاختبار / Testing Steps
1. افتح أي صفحة اختبار
2. جرب تبديل اللغات
3. تحقق من وحدة التحكم (F12)
4. لاحظ تغيير اتجاه النص والخطوط
5. أعد تحميل الصفحة للتأكد من الحفظ

---

## 🚀 التطوير المستقبلي / Future Development

### المخطط له / Planned
- ترجمة المزيد من الصفحات الفرعية
- ترجمة رسائل قواعد البيانات
- إضافة لغات جديدة
- تحسين أداء التحميل

### كيفية إضافة لغة جديدة / Adding New Language
1. أنشئ ملف ترجمة جديد في `translations/`
2. أضف الترجمات بنفس هيكل الملفات الموجودة
3. أضف زر اللغة الجديدة في مفتاح اللغة
4. أنشئ صفحة منفصلة للغة الجديدة (اختياري)
5. حدث دالة `changeLanguage()` لدعم اللغة الجديدة

---

## 📞 الدعم الفني / Technical Support

### استكشاف الأخطاء / Troubleshooting
1. **الترجمات لا تظهر**: تحقق من ملفات JSON ووحدة التحكم
2. **مفتاح اللغة لا يظهر**: تأكد من تحميل language-manager.js
3. **اتجاه النص خاطئ**: تحقق من خصائص dir و lang
4. **اللغة لا تُحفظ**: تأكد من تفعيل localStorage

### رسائل التشخيص / Diagnostic Messages
- `🌐 تغيير اللغة إلى: en`
- `🔄 تطبيق الترجمات...`
- `✅ ترجم: site_title -> Healthcare Centers Management System`
- `📊 تم ترجمة 15 عنصر`

---

## 📝 الخلاصة / Summary

تم تطوير نظام شامل ومتقدم لدعم اللغات المتعددة يوفر:
- **تجربة مستخدم سلسة** مع تبديل فوري للغات
- **هيكل تقني منظم** قابل للتوسع والصيانة
- **ترجمات شاملة** لجميع العناصر الأساسية
- **تصميم متجاوب** يدعم جميع الأجهزة
- **حفظ تلقائي** لتفضيلات المستخدم

النظام جاهز للاستخدام ويمكن توسيعه بسهولة لإضافة المزيد من اللغات والترجمات.
