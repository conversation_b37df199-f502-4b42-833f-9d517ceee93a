<?php
// Système de Gestion des Centres de Santé - Version Française
// Vérifier l'état du système
$isInstalled = file_exists('config.php') && file_exists('database.sql');
$hasDatabase = false;

if ($isInstalled) {
    try {
        include_once 'config.php';
        $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
        $hasDatabase = true;
    } catch (Exception $e) {
        $hasDatabase = false;
    }
}
?>
<!DOCTYPE html>
<html lang="fr" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Système de Gestion des Centres de Santé</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            direction: ltr;
            text-align: left;
        }

        .welcome-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 50px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 900px;
            width: 90%;
            margin: 20px;
        }

        h1 {
            color: #667eea;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 15px;
            text-align: center;
        }

        .subtitle {
            color: #666;
            font-size: 18px;
            margin-bottom: 40px;
            text-align: center;
            line-height: 1.6;
        }

        .status-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #667eea;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-icon {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .status-icon.success {
            color: #28a745;
        }

        .status-icon.error {
            color: #dc3545;
        }

        .status-icon.warning {
            color: #ffc107;
        }

        .features {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #28a745;
        }

        .features h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 20px;
        }

        .features ul {
            list-style: none;
        }

        .features li {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px 0;
            color: #555;
            font-size: 16px;
        }

        .features li i {
            color: #667eea;
            width: 20px;
        }

        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 15px 25px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 500;
            font-size: 16px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .version-info {
            background: #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            font-size: 14px;
            line-height: 1.8;
            color: #495057;
        }

        .language-selector-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 10px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .language-selector {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .language-btn {
            background: transparent;
            border: 2px solid transparent;
            border-radius: 10px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            color: #64748b;
            white-space: nowrap;
        }

        .language-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: rgba(102, 126, 234, 0.3);
            transform: translateY(-2px);
        }

        .language-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        @media (max-width: 768px) {
            .welcome-container {
                margin: 20px;
                padding: 30px 20px;
            }

            .actions {
                grid-template-columns: 1fr;
            }

            h1 {
                font-size: 24px;
            }

            .language-selector-container {
                top: 10px;
                right: 10px;
                padding: 8px;
            }

            .language-btn {
                padding: 6px 8px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <!-- Sélecteur de langue -->
    <div class="language-selector-container">
        <div class="language-selector">
            <button class="language-btn" onclick="changeLanguage('ar')">🇸🇦 العربية</button>
            <button class="language-btn" onclick="changeLanguage('en')">🇺🇸 English</button>
            <button class="language-btn active" onclick="changeLanguage('fr')">🇫🇷 Français</button>
        </div>
    </div>

    <div class="welcome-container">
        <h1>Système de Gestion des Centres de Santé</h1>
        <p class="subtitle">
            Système complet pour la gestion des centres de santé, le suivi des vaccinations, des médicaments et des contraceptifs
        </p>

        <!-- État du système -->
        <div class="status-section">
            <h3 style="margin-bottom: 15px; color: #333;">État du Système</h3>
            
            <div class="status-item">
                <span>État de l'Installation</span>
                <span class="status-icon <?php echo $isInstalled ? 'success' : 'error'; ?>">
                    <i class="fas <?php echo $isInstalled ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                    <span><?php echo $isInstalled ? 'Installé' : 'Non Installé'; ?></span>
                </span>
            </div>
            
            <div class="status-item">
                <span>Base de Données</span>
                <span class="status-icon <?php echo $hasDatabase ? 'success' : 'error'; ?>">
                    <i class="fas <?php echo $hasDatabase ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                    <span><?php echo $hasDatabase ? 'Connectée' : 'Non Connectée'; ?></span>
                </span>
            </div>
            
            <div class="status-item">
                <span>Version PHP</span>
                <span class="status-icon <?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'success' : 'warning'; ?>">
                    <i class="fas fa-info-circle"></i>
                    <?php echo PHP_VERSION; ?>
                </span>
            </div>
        </div>

        <!-- Fonctionnalités -->
        <div class="features">
            <h3>Fonctionnalités Principales</h3>
            <ul>
                <li><i class="fas fa-baby"></i> <span>Gestion complète des enfants et vaccinations</span></li>
                <li><i class="fas fa-syringe"></i> <span>Suivi des vaccins et inventaire</span></li>
                <li><i class="fas fa-pills"></i> <span>Gestion des médicaments et contraceptifs</span></li>
                <li><i class="fas fa-chart-bar"></i> <span>Rapports et statistiques avancés</span></li>
                <li><i class="fas fa-users"></i> <span>Système de permissions multi-niveaux</span></li>
                <li><i class="fas fa-mobile-alt"></i> <span>Compatible avec tous les appareils</span></li>
            </ul>
        </div>

        <!-- Actions -->
        <div class="actions">
            <?php if (!$isInstalled): ?>
                <a href="install.php" class="btn btn-primary">
                    <i class="fas fa-download"></i>
                    <span>Installer le Système</span>
                </a>
            <?php else: ?>
                <a href="cs-manager-api.html" class="btn btn-success">
                    <i class="fas fa-rocket"></i>
                    <span>Nouvelle Version (API)</span>
                </a>
                
                <a href="cs-manager.html" class="btn btn-info">
                    <i class="fas fa-desktop"></i>
                    <span>Ancienne Version</span>
                </a>
            <?php endif; ?>
            
            <a href="system-test.php" class="btn btn-warning">
                <i class="fas fa-vial"></i>
                <span>Test du Système</span>
            </a>
            
            <a href="README.md" class="btn btn-secondary">
                <i class="fas fa-book"></i>
                <span>Guide Utilisateur</span>
            </a>
        </div>

        <!-- Informations de version -->
        <div class="version-info">
            <strong>Version:</strong> 2.0.0 (avec base de données)<br>
            <strong>Date de Mise à Jour:</strong> <?php echo date('Y-m-d'); ?><br>
            <strong>Développeur:</strong> <span>Équipe de Développement des Systèmes de Santé</span>
        </div>

        <?php if ($isInstalled): ?>
        <div style="margin-top: 20px; padding: 15px; background: #d4edda; border-radius: 8px; color: #155724;">
            <i class="fas fa-info-circle"></i>
            <strong>Note:</strong> <span>Le système est prêt à être utilisé. Vous pouvez maintenant accéder à la nouvelle version qui utilise la base de données MySQL.</span>
        </div>
        <?php else: ?>
        <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 8px; color: #856404;">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Avertissement:</strong> <span>Le système doit être installé en premier avant utilisation. Cliquez sur "Installer le Système" pour commencer.</span>
        </div>
        <?php endif; ?>
    </div>

    <script>
        function changeLanguage(lang) {
            localStorage.setItem('selectedLanguage', lang);
            
            if (lang === 'ar') {
                window.location.href = 'index.php';
            } else if (lang === 'en') {
                window.location.href = 'index-en.php';
            }
            // Rester sur la page actuelle pour le français
        }

        // Appliquer la langue sauvegardée au chargement
        document.addEventListener('DOMContentLoaded', function() {
            const savedLang = localStorage.getItem('selectedLanguage') || 'fr';
            if (savedLang !== 'fr') {
                changeLanguage(savedLang);
            }
        });
    </script>
</body>
</html>
