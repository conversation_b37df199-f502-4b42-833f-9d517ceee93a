<?php
/**
 * الصفحة الرئيسية لنظام إدارة المراكز الصحية
 * Main Page for Healthcare Centers Management System
 */

// التحقق من حالة التثبيت
$isInstalled = file_exists('config/installed.lock');
$hasDatabase = file_exists('config/database.php');

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة المراكز الصحية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            direction: rtl;
        }

        .welcome-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            text-align: center;
        }

        .logo {
            font-size: 64px;
            color: #667eea;
            margin-bottom: 20px;
        }

        h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 15px;
        }

        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .status-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: right;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-icon {
            font-size: 20px;
        }

        .status-icon.success {
            color: #28a745;
        }

        .status-icon.error {
            color: #dc3545;
        }

        .status-icon.warning {
            color: #ffc107;
        }

        .actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 30px;
        }

        .btn {
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .version-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            font-size: 14px;
            color: #666;
        }

        .features {
            text-align: right;
            margin: 20px 0;
        }

        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            padding: 8px 0;
            color: #666;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .features li i {
            color: #28a745;
            width: 20px;
        }

        @media (max-width: 768px) {
            .welcome-container {
                margin: 20px;
                padding: 30px 20px;
            }

            .actions {
                grid-template-columns: 1fr;
            }

            h1 {
                font-size: 24px;
            }
        }

        /* دعم اتجاه النص للغات مختلفة */
        [dir="ltr"] .status-section {
            text-align: left;
        }

        [dir="ltr"] .features {
            text-align: left;
        }

        [dir="ltr"] .features li {
            flex-direction: row;
        }

        [dir="ltr"] .status-item {
            flex-direction: row-reverse;
        }

        /* تحسينات للغة الإنجليزية والفرنسية */
        [dir="ltr"] body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        [dir="rtl"] body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
    </style>
</head>
<body>
    <!-- مفتاح تبديل اللغة -->
    <div class="language-selector-container" style="position: fixed; top: 20px; right: 20px; z-index: 1000; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 15px; padding: 10px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); border: 1px solid rgba(255, 255, 255, 0.2);">
        <div class="language-selector" style="display: flex; gap: 8px; align-items: center;">
            <button class="language-btn active" onclick="changeLanguage('ar')" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: 2px solid transparent; border-radius: 10px; padding: 8px 12px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.3s ease; white-space: nowrap;">🇸🇦 العربية</button>
            <button class="language-btn" onclick="changeLanguage('en')" style="background: transparent; border: 2px solid transparent; border-radius: 10px; padding: 8px 12px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.3s ease; color: #64748b; white-space: nowrap;">🇺🇸 English</button>
            <button class="language-btn" onclick="changeLanguage('fr')" style="background: transparent; border: 2px solid transparent; border-radius: 10px; padding: 8px 12px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.3s ease; color: #64748b; white-space: nowrap;">🇫🇷 Français</button>
        </div>
    </div>

    <div class="welcome-container">
        <div class="logo">
            <i class="fas fa-hospital"></i>
        </div>
        
        <h1 data-translate="site_title">نظام إدارة المراكز الصحية</h1>
        <p class="subtitle" data-translate="site_subtitle">
            نظام شامل لإدارة المراكز الصحية وتتبع التلقيحات والأدوية ووسائل منع الحمل
        </p>

        <!-- حالة النظام -->
        <div class="status-section">
            <h3 style="margin-bottom: 15px; color: #333;" data-translate="system_status">حالة النظام</h3>

            <div class="status-item">
                <span data-translate="installation_status">حالة التثبيت</span>
                <span class="status-icon <?php echo $isInstalled ? 'success' : 'error'; ?>">
                    <i class="fas <?php echo $isInstalled ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                    <span data-translate="<?php echo $isInstalled ? 'installed' : 'not_installed'; ?>"><?php echo $isInstalled ? 'مثبت' : 'غير مثبت'; ?></span>
                </span>
            </div>

            <div class="status-item">
                <span data-translate="database_status">قاعدة البيانات</span>
                <span class="status-icon <?php echo $hasDatabase ? 'success' : 'error'; ?>">
                    <i class="fas <?php echo $hasDatabase ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                    <span data-translate="<?php echo $hasDatabase ? 'connected' : 'not_connected'; ?>"><?php echo $hasDatabase ? 'متصلة' : 'غير متصلة'; ?></span>
                </span>
            </div>

            <div class="status-item">
                <span data-translate="php_version">إصدار PHP</span>
                <span class="status-icon <?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'success' : 'warning'; ?>">
                    <i class="fas fa-info-circle"></i>
                    <?php echo PHP_VERSION; ?>
                </span>
            </div>
        </div>

        <!-- المميزات -->
        <div class="features">
            <h3 data-translate="main_features">المميزات الرئيسية</h3>
            <ul>
                <li><i class="fas fa-baby"></i> <span data-translate="children_management">إدارة شاملة للأطفال والتلقيحات</span></li>
                <li><i class="fas fa-syringe"></i> <span data-translate="vaccine_tracking">تتبع اللقاحات والمخزون</span></li>
                <li><i class="fas fa-pills"></i> <span data-translate="medicine_management">إدارة الأدوية ووسائل منع الحمل</span></li>
                <li><i class="fas fa-chart-bar"></i> <span data-translate="reports_statistics">تقارير وإحصائيات متقدمة</span></li>
                <li><i class="fas fa-users"></i> <span data-translate="user_permissions">نظام صلاحيات متعدد المستويات</span></li>
                <li><i class="fas fa-mobile-alt"></i> <span data-translate="mobile_compatible">متوافق مع جميع الأجهزة</span></li>
            </ul>
        </div>

        <!-- الإجراءات -->
        <div class="actions">
            <?php if (!$isInstalled): ?>
                <a href="install.php" class="btn btn-primary">
                    <i class="fas fa-download"></i>
                    <span data-translate="install_system">تثبيت النظام</span>
                </a>
            <?php else: ?>
                <a href="cs-manager-api.html" class="btn btn-success">
                    <i class="fas fa-rocket"></i>
                    <span data-translate="new_version_api">النسخة الجديدة (API)</span>
                </a>

                <a href="cs-manager.html" class="btn btn-info">
                    <i class="fas fa-desktop"></i>
                    <span data-translate="old_version">النسخة القديمة</span>
                </a>
            <?php endif; ?>

            <a href="system-test.php" class="btn btn-warning">
                <i class="fas fa-vial"></i>
                <span data-translate="system_test">اختبار النظام</span>
            </a>

            <a href="README.md" class="btn btn-secondary">
                <i class="fas fa-book"></i>
                <span data-translate="user_guide">دليل المستخدم</span>
            </a>
        </div>

        <!-- معلومات الإصدار -->
        <div class="version-info">
            <strong data-translate="version">الإصدار:</strong> 2.0.0 (مع قاعدة البيانات)<br>
            <strong data-translate="update_date">تاريخ التحديث:</strong> <?php echo date('Y-m-d'); ?><br>
            <strong data-translate="developer">المطور:</strong> <span data-translate="development_team">فريق تطوير الأنظمة الصحية</span>
        </div>

        <?php if ($isInstalled): ?>
        <div style="margin-top: 20px; padding: 15px; background: #d4edda; border-radius: 8px; color: #155724;">
            <i class="fas fa-info-circle"></i>
            <strong data-translate="note">ملاحظة:</strong> <span data-translate="system_ready">النظام جاهز للاستخدام. يمكنك الآن الوصول إلى النسخة الجديدة التي تستخدم قاعدة البيانات MySQL.</span>
        </div>
        <?php else: ?>
        <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 8px; color: #856404;">
            <i class="fas fa-exclamation-triangle"></i>
            <strong data-translate="warning">تنبيه:</strong> <span data-translate="install_first">يجب تثبيت النظام أولاً قبل الاستخدام. انقر على "تثبيت النظام" للبدء.</span>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // دالة تغيير اللغة
        function changeLanguage(lang) {
            // حفظ اللغة المختارة
            localStorage.setItem('selectedLanguage', lang);

            // تحديث اتجاه النص
            const html = document.documentElement;
            const body = document.body;

            if (lang === 'ar') {
                html.setAttribute('lang', 'ar');
                html.setAttribute('dir', 'rtl');
                body.style.direction = 'rtl';
                body.style.fontFamily = "'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
            } else {
                html.setAttribute('lang', lang);
                html.setAttribute('dir', 'ltr');
                body.style.direction = 'ltr';
                body.style.fontFamily = "'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
            }

            // تحديث الأزرار
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.remove('active');
                btn.style.background = 'transparent';
                btn.style.color = '#64748b';
            });

            // تفعيل الزر المختار
            event.target.classList.add('active');
            event.target.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            event.target.style.color = 'white';

            // تطبيق الترجمات إذا كان مدير اللغات متوفر
            if (window.languageManager) {
                window.languageManager.changeLanguage(lang);
            } else {
                // ترجمات بسيطة
                const translations = {
                    ar: {
                        'site_title': 'نظام إدارة المراكز الصحية',
                        'site_subtitle': 'نظام شامل لإدارة المراكز الصحية وتتبع التلقيحات والأدوية ووسائل منع الحمل',
                        'system_status': 'حالة النظام'
                    },
                    en: {
                        'site_title': 'Healthcare Centers Management System',
                        'site_subtitle': 'Comprehensive system for managing healthcare centers, tracking vaccinations, medicines, and contraceptives',
                        'system_status': 'System Status'
                    },
                    fr: {
                        'site_title': 'Système de Gestion des Centres de Santé',
                        'site_subtitle': 'Système complet pour la gestion des centres de santé, le suivi des vaccinations, des médicaments et des contraceptifs',
                        'system_status': 'État du Système'
                    }
                };

                // تطبيق الترجمات
                document.querySelectorAll('[data-translate]').forEach(element => {
                    const key = element.getAttribute('data-translate');
                    if (translations[lang] && translations[lang][key]) {
                        element.textContent = translations[lang][key];
                    }
                });

                // تحديث العنوان
                if (translations[lang] && translations[lang]['site_title']) {
                    document.title = translations[lang]['site_title'];
                }
            }
        }

        // إضافة تأثيرات تفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تطبيق اللغة المحفوظة
            const savedLang = localStorage.getItem('selectedLanguage') || 'ar';
            if (savedLang !== 'ar') {
                // محاكاة النقر على زر اللغة المحفوظة
                const langBtn = document.querySelector(`[onclick="changeLanguage('${savedLang}')"]`);
                if (langBtn) {
                    langBtn.click();
                }
            }

            // تأثير hover للأزرار
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
                });
                
                button.addEventListener('mouseleave', function() {
                    this.style.boxShadow = 'none';
                });
            });

            // فحص حالة النظام كل 30 ثانية
            setInterval(function() {
                // يمكن إضافة فحص AJAX هنا لتحديث حالة النظام
            }, 30000);
        });
    </script>

    <!-- تحميل نظام إدارة اللغات -->
    <script src="js/language-manager.js"></script>
</body>
</html>
