/**
 * نظام إدارة اللغات - Language Management System
 * يدعم العربية والإنجليزية والفرنسية
 * Supports Arabic, English, and French
 */

class LanguageManager {
    constructor() {
        this.currentLanguage = this.getStoredLanguage() || 'ar';
        this.translations = {};
        this.loadTranslations();
        this.initializeLanguageSelector();
    }

    // الحصول على اللغة المحفوظة من localStorage
    getStoredLanguage() {
        return localStorage.getItem('selectedLanguage');
    }

    // حفظ اللغة في localStorage
    setStoredLanguage(language) {
        localStorage.setItem('selectedLanguage', language);
    }

    // تحميل الترجمات
    loadTranslations() {
        this.translations = {
            ar: {
                // الصفحة الرئيسية
                'site_title': 'نظام إدارة المراكز الصحية',
                'site_subtitle': 'نظام شامل لإدارة المراكز الصحية وتتبع التلقيحات والأدوية ووسائل منع الحمل',
                'system_status': 'حالة النظام',
                'installation_status': 'حالة التثبيت',
                'database_status': 'قاعدة البيانات',
                'php_version': 'إصدار PHP',
                'installed': 'مثبت',
                'not_installed': 'غير مثبت',
                'connected': 'متصلة',
                'not_connected': 'غير متصلة',
                'main_features': 'المميزات الرئيسية',
                'children_management': 'إدارة شاملة للأطفال والتلقيحات',
                'vaccine_tracking': 'تتبع اللقاحات والمخزون',
                'medicine_management': 'إدارة الأدوية ووسائل منع الحمل',
                'reports_statistics': 'تقارير وإحصائيات متقدمة',
                'user_permissions': 'نظام صلاحيات متعدد المستويات',
                'mobile_compatible': 'متوافق مع جميع الأجهزة',
                'install_system': 'تثبيت النظام',
                'new_version_api': 'النسخة الجديدة (API)',
                'old_version': 'النسخة القديمة',
                'system_test': 'اختبار النظام',
                'user_guide': 'دليل المستخدم',
                'version': 'الإصدار',
                'update_date': 'تاريخ التحديث',
                'developer': 'المطور',
                'development_team': 'فريق تطوير الأنظمة الصحية',
                'note': 'ملاحظة',
                'system_ready': 'النظام جاهز للاستخدام. يمكنك الآن الوصول إلى النسخة الجديدة التي تستخدم قاعدة البيانات MySQL.',
                'warning': 'تنبيه',
                'install_first': 'يجب تثبيت النظام أولاً قبل الاستخدام. انقر على "تثبيت النظام" للبدء.',
                'language': 'اللغة',

                // واجهة إدارة المراكز الصحية
                'welcome': 'مرحباً بك',
                'nurse': 'الممرض/ة',
                'center': 'المركز',
                'region': 'الإقليم',
                'logout': 'تسجيل الخروج',
                'username': 'اسم المستخدم',
                'password': 'كلمة المرور',
                'login': 'تسجيل الدخول',
                'register': 'إنشاء الحساب',
                'nurse_name': 'اسم الممرض/ة',
                'center_name': 'اسم المركز الصحي',
                'account_type': 'نوع الحساب',
                'home_page': 'الصفحة الرئيسية',
                'vaccination_calculator': 'حاسبة التلقيح',
                'vaccine_management': 'تدبير اللقاحات',
                'medicine_management': 'تدبير الأدوية',
                'family_planning': 'تنظيم الأسرة',
                'children_registry': 'سجل الأطفال',
                'tasks_list': 'قائمة المهام',
                'messages': 'الرسائل',
                'account_management': 'إدارة الحسابات',
                'notifications': 'الإشعارات',
                'calculator': 'الآلة الحاسبة',
                'new_message': 'رسالة جديدة',
                'recipient': 'المرسل إليه',
                'message': 'الرسالة',
                'attachments': 'المرفقات',
                'cancel': 'إلغاء',
                'send': 'إرسال',
                'save': 'حفظ',
                'delete': 'حذف',
                'edit': 'تعديل',
                'add': 'إضافة',
                'close': 'إغلاق',
                'loading': 'جاري التحميل...',
                'no_data': 'لا توجد بيانات',
                'error': 'خطأ',
                'success': 'نجح',
                'confirm': 'تأكيد',
                'yes': 'نعم',
                'no': 'لا'
            },
            en: {
                // Main Page
                'site_title': 'Healthcare Centers Management System',
                'site_subtitle': 'Comprehensive system for managing healthcare centers, tracking vaccinations, medicines, and contraceptives',
                'system_status': 'System Status',
                'installation_status': 'Installation Status',
                'database_status': 'Database',
                'php_version': 'PHP Version',
                'installed': 'Installed',
                'not_installed': 'Not Installed',
                'connected': 'Connected',
                'not_connected': 'Not Connected',
                'main_features': 'Main Features',
                'children_management': 'Comprehensive children and vaccination management',
                'vaccine_tracking': 'Vaccine and inventory tracking',
                'medicine_management': 'Medicine and contraceptive management',
                'reports_statistics': 'Advanced reports and statistics',
                'user_permissions': 'Multi-level permissions system',
                'mobile_compatible': 'Compatible with all devices',
                'install_system': 'Install System',
                'new_version_api': 'New Version (API)',
                'old_version': 'Old Version',
                'system_test': 'System Test',
                'user_guide': 'User Guide',
                'version': 'Version',
                'update_date': 'Update Date',
                'developer': 'Developer',
                'development_team': 'Healthcare Systems Development Team',
                'note': 'Note',
                'system_ready': 'System is ready for use. You can now access the new version that uses MySQL database.',
                'warning': 'Warning',
                'install_first': 'System must be installed first before use. Click "Install System" to begin.',
                'language': 'Language',

                // Healthcare Centers Management Interface
                'welcome': 'Welcome',
                'nurse': 'Nurse',
                'center': 'Center',
                'region': 'Region',
                'logout': 'Logout',
                'username': 'Username',
                'password': 'Password',
                'login': 'Login',
                'register': 'Create Account',
                'nurse_name': 'Nurse Name',
                'center_name': 'Healthcare Center Name',
                'account_type': 'Account Type',
                'home_page': 'Home Page',
                'vaccination_calculator': 'Vaccination Calculator',
                'vaccine_management': 'Vaccine Management',
                'medicine_management': 'Medicine Management',
                'family_planning': 'Family Planning',
                'children_registry': 'Children Registry',
                'tasks_list': 'Tasks List',
                'messages': 'Messages',
                'account_management': 'Account Management',
                'notifications': 'Notifications',
                'calculator': 'Calculator',
                'new_message': 'New Message',
                'recipient': 'Recipient',
                'message': 'Message',
                'attachments': 'Attachments',
                'cancel': 'Cancel',
                'send': 'Send',
                'save': 'Save',
                'delete': 'Delete',
                'edit': 'Edit',
                'add': 'Add',
                'close': 'Close',
                'loading': 'Loading...',
                'no_data': 'No data',
                'error': 'Error',
                'success': 'Success',
                'confirm': 'Confirm',
                'yes': 'Yes',
                'no': 'No'
            },
            fr: {
                // Page Principale
                'site_title': 'Système de Gestion des Centres de Santé',
                'site_subtitle': 'Système complet pour la gestion des centres de santé, le suivi des vaccinations, des médicaments et des contraceptifs',
                'system_status': 'État du Système',
                'installation_status': 'État de l\'Installation',
                'database_status': 'Base de Données',
                'php_version': 'Version PHP',
                'installed': 'Installé',
                'not_installed': 'Non Installé',
                'connected': 'Connectée',
                'not_connected': 'Non Connectée',
                'main_features': 'Fonctionnalités Principales',
                'children_management': 'Gestion complète des enfants et vaccinations',
                'vaccine_tracking': 'Suivi des vaccins et inventaire',
                'medicine_management': 'Gestion des médicaments et contraceptifs',
                'reports_statistics': 'Rapports et statistiques avancés',
                'user_permissions': 'Système de permissions multi-niveaux',
                'mobile_compatible': 'Compatible avec tous les appareils',
                'install_system': 'Installer le Système',
                'new_version_api': 'Nouvelle Version (API)',
                'old_version': 'Ancienne Version',
                'system_test': 'Test du Système',
                'user_guide': 'Guide Utilisateur',
                'version': 'Version',
                'update_date': 'Date de Mise à Jour',
                'developer': 'Développeur',
                'development_team': 'Équipe de Développement des Systèmes de Santé',
                'note': 'Note',
                'system_ready': 'Le système est prêt à être utilisé. Vous pouvez maintenant accéder à la nouvelle version qui utilise la base de données MySQL.',
                'warning': 'Avertissement',
                'install_first': 'Le système doit être installé en premier avant utilisation. Cliquez sur "Installer le Système" pour commencer.',
                'language': 'Langue',

                // Interface de Gestion des Centres de Santé
                'welcome': 'Bienvenue',
                'nurse': 'Infirmier/ère',
                'center': 'Centre',
                'region': 'Région',
                'logout': 'Déconnexion',
                'username': 'Nom d\'utilisateur',
                'password': 'Mot de passe',
                'login': 'Connexion',
                'register': 'Créer un compte',
                'nurse_name': 'Nom de l\'infirmier/ère',
                'center_name': 'Nom du centre de santé',
                'account_type': 'Type de compte',
                'home_page': 'Page d\'accueil',
                'vaccination_calculator': 'Calculateur de vaccination',
                'vaccine_management': 'Gestion des vaccins',
                'medicine_management': 'Gestion des médicaments',
                'family_planning': 'Planification familiale',
                'children_registry': 'Registre des enfants',
                'tasks_list': 'Liste des tâches',
                'messages': 'Messages',
                'account_management': 'Gestion des comptes',
                'notifications': 'Notifications',
                'calculator': 'Calculatrice',
                'new_message': 'Nouveau message',
                'recipient': 'Destinataire',
                'message': 'Message',
                'attachments': 'Pièces jointes',
                'cancel': 'Annuler',
                'send': 'Envoyer',
                'save': 'Sauvegarder',
                'delete': 'Supprimer',
                'edit': 'Modifier',
                'add': 'Ajouter',
                'close': 'Fermer',
                'loading': 'Chargement...',
                'no_data': 'Aucune donnée',
                'error': 'Erreur',
                'success': 'Succès',
                'confirm': 'Confirmer',
                'yes': 'Oui',
                'no': 'Non'
            }
        };
    }

    // الحصول على الترجمة
    translate(key) {
        return this.translations[this.currentLanguage]?.[key] || key;
    }

    // تغيير اللغة
    changeLanguage(language) {
        if (this.translations[language]) {
            this.currentLanguage = language;
            this.setStoredLanguage(language);
            this.updatePageLanguage();
            this.updateLanguageSelector();
        }
    }

    // تحديث لغة الصفحة
    updatePageLanguage() {
        // تحديث اتجاه النص
        const html = document.documentElement;
        const body = document.body;
        
        if (this.currentLanguage === 'ar') {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            body.style.direction = 'rtl';
        } else {
            html.setAttribute('lang', this.currentLanguage);
            html.setAttribute('dir', 'ltr');
            body.style.direction = 'ltr';
        }

        // تحديث النصوص
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.getAttribute('data-translate');
            element.textContent = this.translate(key);
        });

        // تحديث العنوان
        document.title = this.translate('site_title');
    }

    // إنشاء مفتاح تبديل اللغة
    initializeLanguageSelector() {
        // التأكد من عدم وجود مفتاح لغة مسبقاً
        const existingSelector = document.querySelector('.language-selector-container');
        if (existingSelector) {
            existingSelector.remove();
        }

        const languageSelector = this.createLanguageSelector();

        // إنشاء حاوي لمفتاح اللغة
        const languageContainer = document.createElement('div');
        languageContainer.className = 'language-selector-container';
        languageContainer.appendChild(languageSelector);

        // إدراج في body مباشرة
        document.body.appendChild(languageContainer);

        console.log('✅ تم إنشاء مفتاح تبديل اللغة بنجاح');
    }

    // إنشاء عنصر اختيار اللغة
    createLanguageSelector() {
        const container = document.createElement('div');
        container.className = 'language-selector';
        
        const languages = [
            { code: 'ar', name: 'العربية', flag: '🇸🇦' },
            { code: 'en', name: 'English', flag: '🇺🇸' },
            { code: 'fr', name: 'Français', flag: '🇫🇷' }
        ];

        languages.forEach(lang => {
            const button = document.createElement('button');
            button.className = `language-btn ${lang.code === this.currentLanguage ? 'active' : ''}`;
            button.innerHTML = `${lang.flag} ${lang.name}`;
            button.onclick = () => this.changeLanguage(lang.code);
            container.appendChild(button);
        });

        return container;
    }

    // تحديث مفتاح اللغة
    updateLanguageSelector() {
        document.querySelectorAll('.language-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        const activeBtn = document.querySelector(`.language-btn:nth-child(${this.getLanguageIndex() + 1})`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
    }

    // الحصول على فهرس اللغة
    getLanguageIndex() {
        const languages = ['ar', 'en', 'fr'];
        return languages.indexOf(this.currentLanguage);
    }

    // تهيئة النظام
    init() {
        // تطبيق اللغة المحفوظة
        this.updatePageLanguage();
        
        // إضافة CSS للغات
        this.addLanguageStyles();
    }

    // إضافة أنماط CSS للغات
    addLanguageStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .language-selector-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 10px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .language-selector {
                display: flex;
                gap: 8px;
                align-items: center;
            }

            .language-btn {
                background: transparent;
                border: 2px solid transparent;
                border-radius: 10px;
                padding: 8px 12px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.3s ease;
                color: #64748b;
                white-space: nowrap;
            }

            .language-btn:hover {
                background: rgba(102, 126, 234, 0.1);
                border-color: rgba(102, 126, 234, 0.3);
                transform: translateY(-2px);
            }

            .language-btn.active {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-color: transparent;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }

            /* تعديل للشاشات الصغيرة */
            @media (max-width: 768px) {
                .language-selector-container {
                    top: 10px;
                    right: 10px;
                    padding: 8px;
                }

                .language-btn {
                    padding: 6px 8px;
                    font-size: 12px;
                }
            }

            /* دعم RTL */
            [dir="rtl"] .language-selector-container {
                right: auto;
                left: 20px;
            }

            [dir="rtl"] .language-selector {
                flex-direction: row-reverse;
            }

            @media (max-width: 768px) {
                [dir="rtl"] .language-selector-container {
                    left: 10px;
                }
            }

            /* دعم شامل لاتجاه النص */
            [dir="ltr"] {
                text-align: left;
            }

            [dir="rtl"] {
                text-align: right;
            }

            /* تحسينات للخطوط حسب اللغة */
            [lang="ar"] {
                font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }

            [lang="en"], [lang="fr"] {
                font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }

            /* تحسينات للقوائم والعناصر */
            [dir="ltr"] .sidebar-menu-item {
                text-align: left;
            }

            [dir="ltr"] .form-group-sidebar {
                text-align: left;
            }

            [dir="ltr"] .auth-form {
                text-align: left;
            }

            [dir="ltr"] .user-info {
                text-align: left;
            }

            /* تحسينات للأزرار */
            [dir="ltr"] .btn, [dir="ltr"] .sidebar-btn {
                text-align: center;
            }

            /* تحسينات للجداول */
            [dir="ltr"] table {
                text-align: left;
            }

            [dir="ltr"] th, [dir="ltr"] td {
                text-align: left;
            }

            /* تحسينات للنماذج */
            [dir="ltr"] input, [dir="ltr"] textarea, [dir="ltr"] select {
                text-align: left;
            }

            [dir="rtl"] input, [dir="rtl"] textarea, [dir="rtl"] select {
                text-align: right;
            }

            /* تحسينات للرسائل والإشعارات */
            [dir="ltr"] .toast {
                text-align: left;
            }

            [dir="ltr"] .modal-content {
                text-align: left;
            }

            /* تحسينات للقوائم المنسدلة */
            [dir="ltr"] .dropdown-menu {
                text-align: left;
            }

            /* تحسينات للبطاقات */
            [dir="ltr"] .card, [dir="ltr"] .service-card {
                text-align: left;
            }

            /* تحسينات للإحصائيات */
            [dir="ltr"] .stat-card, [dir="ltr"] .stat-item {
                text-align: center;
            }
        `;
        document.head.appendChild(style);
    }
}

// تهيئة مدير اللغات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌐 بدء تهيئة نظام إدارة اللغات...');
    try {
        window.languageManager = new LanguageManager();
        window.languageManager.init();
        console.log('✅ تم تهيئة نظام إدارة اللغات بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تهيئة نظام إدارة اللغات:', error);
    }
});

// تهيئة إضافية في حالة تأخر تحميل DOM
if (document.readyState === 'loading') {
    // DOM لم يتم تحميله بعد
    document.addEventListener('DOMContentLoaded', initLanguageManager);
} else {
    // DOM تم تحميله بالفعل
    initLanguageManager();
}

function initLanguageManager() {
    if (!window.languageManager) {
        console.log('🔄 تهيئة مدير اللغات...');
        try {
            window.languageManager = new LanguageManager();
            window.languageManager.init();
            console.log('✅ تم تهيئة مدير اللغات');
        } catch (error) {
            console.error('❌ خطأ في تهيئة مدير اللغات:', error);
        }
    }
}

// دالة مساعدة للترجمة
function t(key) {
    return window.languageManager ? window.languageManager.translate(key) : key;
}
