/**
 * نظام إدارة اللغات - Language Management System
 * يدعم العربية والإنجليزية والفرنسية
 * Supports Arabic, English, and French
 */

class LanguageManager {
    constructor() {
        this.currentLanguage = this.getStoredLanguage() || 'ar';
        this.translations = {};
        this.isLoaded = false;
        this.init();
    }

    async init() {
        try {
            await this.loadTranslations();
            this.initializeLanguageSelector();
            this.updatePageLanguage();
            this.addLanguageStyles();
            this.isLoaded = true;
            console.log('✅ تم تهيئة نظام إدارة اللغات بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام إدارة اللغات:', error);
        }
    }

    // الحصول على اللغة المحفوظة من localStorage
    getStoredLanguage() {
        return localStorage.getItem('selectedLanguage');
    }

    // حفظ اللغة في localStorage
    setStoredLanguage(language) {
        localStorage.setItem('selectedLanguage', language);
    }

    // تحميل الترجمات
    async loadTranslations() {
        try {
            // محاولة تحميل الترجمات من ملفات JSON
            const languages = ['ar', 'en', 'fr'];
            const loadPromises = languages.map(async (lang) => {
                try {
                    const response = await fetch(`translations/${lang}.json`);
                    if (response.ok) {
                        const data = await response.json();
                        return { lang, data: this.flattenTranslations(data) };
                    }
                } catch (error) {
                    console.warn(`⚠️ لم يتم العثور على ملف الترجمة: translations/${lang}.json`);
                }
                return { lang, data: null };
            });

            const results = await Promise.all(loadPromises);

            // تجميع الترجمات المحملة
            this.translations = {};
            results.forEach(({ lang, data }) => {
                if (data) {
                    this.translations[lang] = data;
                    console.log(`✅ تم تحميل ترجمات ${lang} من الملف`);
                }
            });

            // إذا لم يتم تحميل أي ترجمات، استخدم الترجمات المدمجة
            if (Object.keys(this.translations).length === 0) {
                console.log('📦 استخدام الترجمات المدمجة...');
                this.loadFallbackTranslations();
            }

        } catch (error) {
            console.error('❌ خطأ في تحميل الترجمات:', error);
            this.loadFallbackTranslations();
        }
    }

    // تحويل الترجمات المتداخلة إلى مسطحة
    flattenTranslations(obj, prefix = '') {
        const flattened = {};
        for (const key in obj) {
            if (typeof obj[key] === 'object' && obj[key] !== null) {
                Object.assign(flattened, this.flattenTranslations(obj[key], prefix + key + '_'));
            } else {
                flattened[prefix + key] = obj[key];
            }
        }
        return flattened;
    }

    // الترجمات الاحتياطية المدمجة
    loadFallbackTranslations() {
        this.translations = {
            ar: {
                // الصفحة الرئيسية
                'main_page_site_title': 'نظام إدارة المراكز الصحية',
                'main_page_site_subtitle': 'نظام شامل لإدارة المراكز الصحية وتتبع التلقيحات والأدوية ووسائل منع الحمل',
                'main_page_system_status': 'حالة النظام',
                'main_page_installation_status': 'حالة التثبيت',
                'main_page_database_status': 'قاعدة البيانات',
                'main_page_php_version': 'إصدار PHP',
                'main_page_installed': 'مثبت',
                'main_page_not_installed': 'غير مثبت',
                'main_page_connected': 'متصلة',
                'main_page_not_connected': 'غير متصلة',
                'main_page_main_features': 'المميزات الرئيسية',
                'main_page_children_management': 'إدارة شاملة للأطفال والتلقيحات',
                'main_page_vaccine_tracking': 'تتبع اللقاحات والمخزون',
                'main_page_medicine_management': 'إدارة الأدوية ووسائل منع الحمل',
                'main_page_reports_statistics': 'تقارير وإحصائيات متقدمة',
                'main_page_user_permissions': 'نظام صلاحيات متعدد المستويات',
                'main_page_mobile_compatible': 'متوافق مع جميع الأجهزة',
                'main_page_install_system': 'تثبيت النظام',
                'main_page_new_version_api': 'النسخة الجديدة (API)',
                'main_page_old_version': 'النسخة القديمة',
                'main_page_system_test': 'اختبار النظام',
                'main_page_user_guide': 'دليل المستخدم',
                'main_page_version': 'الإصدار',
                'main_page_update_date': 'تاريخ التحديث',
                'main_page_developer': 'المطور',
                'main_page_development_team': 'فريق تطوير الأنظمة الصحية',
                'main_page_note': 'ملاحظة',
                'main_page_system_ready': 'النظام جاهز للاستخدام. يمكنك الآن الوصول إلى النسخة الجديدة التي تستخدم قاعدة البيانات MySQL.',
                'main_page_warning': 'تنبيه',
                'main_page_install_first': 'يجب تثبيت النظام أولاً قبل الاستخدام. انقر على "تثبيت النظام" للبدء.',
                'common_language': 'اللغة',

                // واجهة إدارة المراكز الصحية
                'healthcare_management_welcome': 'مرحباً بك',
                'healthcare_management_nurse': 'الممرض/ة',
                'healthcare_management_center': 'المركز',
                'healthcare_management_region': 'الإقليم',
                'healthcare_management_logout': 'تسجيل الخروج',
                'healthcare_management_username': 'اسم المستخدم',
                'healthcare_management_password': 'كلمة المرور',
                'healthcare_management_login': 'تسجيل الدخول',
                'healthcare_management_register': 'إنشاء الحساب',
                'healthcare_management_nurse_name': 'اسم الممرض/ة',
                'healthcare_management_center_name': 'اسم المركز الصحي',
                'healthcare_management_account_type': 'نوع الحساب',
                'healthcare_management_home_page': 'الصفحة الرئيسية',
                'healthcare_management_vaccination_calculator': 'حاسبة التلقيح',
                'healthcare_management_vaccine_management': 'تدبير اللقاحات',
                'healthcare_management_medicine_management': 'تدبير الأدوية',
                'healthcare_management_family_planning': 'تنظيم الأسرة',
                'healthcare_management_children_registry': 'سجل الأطفال',
                'healthcare_management_tasks_list': 'قائمة المهام',
                'healthcare_management_messages': 'الرسائل',
                'healthcare_management_account_management': 'إدارة الحسابات',
                'healthcare_management_notifications': 'الإشعارات',
                'healthcare_management_calculator': 'الآلة الحاسبة',
                'healthcare_management_new_message': 'رسالة جديدة',
                'healthcare_management_recipient': 'المرسل إليه',
                'healthcare_management_message': 'الرسالة',
                'healthcare_management_attachments': 'المرفقات',
                'common_cancel': 'إلغاء',
                'healthcare_management_send': 'إرسال',
                'common_save': 'حفظ',
                'common_delete': 'حذف',
                'common_edit': 'تعديل',
                'common_add': 'إضافة',
                'common_close': 'إغلاق',
                'common_loading': 'جاري التحميل...',
                'common_no_data': 'لا توجد بيانات',
                'common_error': 'خطأ',
                'common_success': 'نجح',
                'common_confirm': 'تأكيد',
                'common_yes': 'نعم',
                'common_no': 'لا'
            },
            en: {
                // Main Page
                'main_page_site_title': 'Healthcare Centers Management System',
                'main_page_site_subtitle': 'Comprehensive system for managing healthcare centers, tracking vaccinations, medicines, and contraceptives',
                'main_page_system_status': 'System Status',
                'main_page_installation_status': 'Installation Status',
                'main_page_database_status': 'Database',
                'main_page_php_version': 'PHP Version',
                'main_page_installed': 'Installed',
                'main_page_not_installed': 'Not Installed',
                'main_page_connected': 'Connected',
                'main_page_not_connected': 'Not Connected',
                'main_page_main_features': 'Main Features',
                'main_page_children_management': 'Comprehensive children and vaccination management',
                'main_page_vaccine_tracking': 'Vaccine and inventory tracking',
                'main_page_medicine_management': 'Medicine and contraceptive management',
                'main_page_reports_statistics': 'Advanced reports and statistics',
                'main_page_user_permissions': 'Multi-level permissions system',
                'main_page_mobile_compatible': 'Compatible with all devices',
                'main_page_install_system': 'Install System',
                'main_page_new_version_api': 'New Version (API)',
                'main_page_old_version': 'Old Version',
                'main_page_system_test': 'System Test',
                'main_page_user_guide': 'User Guide',
                'main_page_version': 'Version',
                'main_page_update_date': 'Update Date',
                'main_page_developer': 'Developer',
                'main_page_development_team': 'Healthcare Systems Development Team',
                'main_page_note': 'Note',
                'main_page_system_ready': 'System is ready for use. You can now access the new version that uses MySQL database.',
                'main_page_warning': 'Warning',
                'main_page_install_first': 'System must be installed first before use. Click "Install System" to begin.',
                'common_language': 'Language',

                // Healthcare Centers Management Interface
                'healthcare_management_welcome': 'Welcome',
                'healthcare_management_nurse': 'Nurse',
                'healthcare_management_center': 'Center',
                'healthcare_management_region': 'Region',
                'healthcare_management_logout': 'Logout',
                'healthcare_management_username': 'Username',
                'healthcare_management_password': 'Password',
                'healthcare_management_login': 'Login',
                'healthcare_management_register': 'Create Account',
                'healthcare_management_nurse_name': 'Nurse Name',
                'healthcare_management_center_name': 'Healthcare Center Name',
                'healthcare_management_account_type': 'Account Type',
                'healthcare_management_home_page': 'Home Page',
                'healthcare_management_vaccination_calculator': 'Vaccination Calculator',
                'healthcare_management_vaccine_management': 'Vaccine Management',
                'healthcare_management_medicine_management': 'Medicine Management',
                'healthcare_management_family_planning': 'Family Planning',
                'healthcare_management_children_registry': 'Children Registry',
                'healthcare_management_tasks_list': 'Tasks List',
                'healthcare_management_messages': 'Messages',
                'healthcare_management_account_management': 'Account Management',
                'healthcare_management_notifications': 'Notifications',
                'healthcare_management_calculator': 'Calculator',
                'healthcare_management_new_message': 'New Message',
                'healthcare_management_recipient': 'Recipient',
                'healthcare_management_message': 'Message',
                'healthcare_management_attachments': 'Attachments',
                'common_cancel': 'Cancel',
                'healthcare_management_send': 'Send',
                'common_save': 'Save',
                'common_delete': 'Delete',
                'common_edit': 'Edit',
                'common_add': 'Add',
                'common_close': 'Close',
                'common_loading': 'Loading...',
                'common_no_data': 'No data',
                'common_error': 'Error',
                'common_success': 'Success',
                'common_confirm': 'Confirm',
                'common_yes': 'Yes',
                'common_no': 'No'
            },
            fr: {
                // Page Principale
                'main_page_site_title': 'Système de Gestion des Centres de Santé',
                'main_page_site_subtitle': 'Système complet pour la gestion des centres de santé, le suivi des vaccinations, des médicaments et des contraceptifs',
                'main_page_system_status': 'État du Système',
                'main_page_installation_status': 'État de l\'Installation',
                'main_page_database_status': 'Base de Données',
                'main_page_php_version': 'Version PHP',
                'main_page_installed': 'Installé',
                'main_page_not_installed': 'Non Installé',
                'main_page_connected': 'Connectée',
                'main_page_not_connected': 'Non Connectée',
                'main_page_main_features': 'Fonctionnalités Principales',
                'main_page_children_management': 'Gestion complète des enfants et vaccinations',
                'main_page_vaccine_tracking': 'Suivi des vaccins et inventaire',
                'main_page_medicine_management': 'Gestion des médicaments et contraceptifs',
                'main_page_reports_statistics': 'Rapports et statistiques avancés',
                'main_page_user_permissions': 'Système de permissions multi-niveaux',
                'main_page_mobile_compatible': 'Compatible avec tous les appareils',
                'main_page_install_system': 'Installer le Système',
                'main_page_new_version_api': 'Nouvelle Version (API)',
                'main_page_old_version': 'Ancienne Version',
                'main_page_system_test': 'Test du Système',
                'main_page_user_guide': 'Guide Utilisateur',
                'main_page_version': 'Version',
                'main_page_update_date': 'Date de Mise à Jour',
                'main_page_developer': 'Développeur',
                'main_page_development_team': 'Équipe de Développement des Systèmes de Santé',
                'main_page_note': 'Note',
                'main_page_system_ready': 'Le système est prêt à être utilisé. Vous pouvez maintenant accéder à la nouvelle version qui utilise la base de données MySQL.',
                'main_page_warning': 'Avertissement',
                'main_page_install_first': 'Le système doit être installé en premier avant utilisation. Cliquez sur "Installer le Système" pour commencer.',
                'common_language': 'Langue',

                // Interface de Gestion des Centres de Santé
                'healthcare_management_welcome': 'Bienvenue',
                'healthcare_management_nurse': 'Infirmier/ère',
                'healthcare_management_center': 'Centre',
                'healthcare_management_region': 'Région',
                'healthcare_management_logout': 'Déconnexion',
                'healthcare_management_username': 'Nom d\'utilisateur',
                'healthcare_management_password': 'Mot de passe',
                'healthcare_management_login': 'Connexion',
                'healthcare_management_register': 'Créer un compte',
                'healthcare_management_nurse_name': 'Nom de l\'infirmier/ère',
                'healthcare_management_center_name': 'Nom du centre de santé',
                'healthcare_management_account_type': 'Type de compte',
                'healthcare_management_home_page': 'Page d\'accueil',
                'healthcare_management_vaccination_calculator': 'Calculateur de vaccination',
                'healthcare_management_vaccine_management': 'Gestion des vaccins',
                'healthcare_management_medicine_management': 'Gestion des médicaments',
                'healthcare_management_family_planning': 'Planification familiale',
                'healthcare_management_children_registry': 'Registre des enfants',
                'healthcare_management_tasks_list': 'Liste des tâches',
                'healthcare_management_messages': 'Messages',
                'healthcare_management_account_management': 'Gestion des comptes',
                'healthcare_management_notifications': 'Notifications',
                'healthcare_management_calculator': 'Calculatrice',
                'healthcare_management_new_message': 'Nouveau message',
                'healthcare_management_recipient': 'Destinataire',
                'healthcare_management_message': 'Message',
                'healthcare_management_attachments': 'Pièces jointes',
                'common_cancel': 'Annuler',
                'healthcare_management_send': 'Envoyer',
                'common_save': 'Sauvegarder',
                'common_delete': 'Supprimer',
                'common_edit': 'Modifier',
                'common_add': 'Ajouter',
                'common_close': 'Fermer',
                'common_loading': 'Chargement...',
                'common_no_data': 'Aucune donnée',
                'common_error': 'Erreur',
                'common_success': 'Succès',
                'common_confirm': 'Confirmer',
                'common_yes': 'Oui',
                'common_no': 'Non'
            }
        };
    }
        };
    }

    // الحصول على الترجمة
    translate(key) {
        return this.translations[this.currentLanguage]?.[key] || key;
    }

    // تغيير اللغة
    changeLanguage(language) {
        if (this.translations[language]) {
            this.currentLanguage = language;
            this.setStoredLanguage(language);
            this.updatePageLanguage();
            this.updateLanguageSelector();
        }
    }

    // تحديث لغة الصفحة
    updatePageLanguage() {
        // تحديث اتجاه النص
        const html = document.documentElement;
        const body = document.body;
        
        if (this.currentLanguage === 'ar') {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            body.style.direction = 'rtl';
        } else {
            html.setAttribute('lang', this.currentLanguage);
            html.setAttribute('dir', 'ltr');
            body.style.direction = 'ltr';
        }

        // تحديث النصوص
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.getAttribute('data-translate');
            element.textContent = this.translate(key);
        });

        // تحديث العنوان
        document.title = this.translate('site_title');
    }

    // إنشاء مفتاح تبديل اللغة
    initializeLanguageSelector() {
        // التأكد من عدم وجود مفتاح لغة مسبقاً
        const existingSelector = document.querySelector('.language-selector-container');
        if (existingSelector) {
            existingSelector.remove();
        }

        const languageSelector = this.createLanguageSelector();

        // إنشاء حاوي لمفتاح اللغة
        const languageContainer = document.createElement('div');
        languageContainer.className = 'language-selector-container';
        languageContainer.appendChild(languageSelector);

        // إدراج في body مباشرة
        document.body.appendChild(languageContainer);

        console.log('✅ تم إنشاء مفتاح تبديل اللغة بنجاح');
    }

    // إنشاء عنصر اختيار اللغة
    createLanguageSelector() {
        const container = document.createElement('div');
        container.className = 'language-selector';
        
        const languages = [
            { code: 'ar', name: 'العربية', flag: '🇸🇦' },
            { code: 'en', name: 'English', flag: '🇺🇸' },
            { code: 'fr', name: 'Français', flag: '🇫🇷' }
        ];

        languages.forEach(lang => {
            const button = document.createElement('button');
            button.className = `language-btn ${lang.code === this.currentLanguage ? 'active' : ''}`;
            button.innerHTML = `${lang.flag} ${lang.name}`;
            button.onclick = () => this.changeLanguage(lang.code);
            container.appendChild(button);
        });

        return container;
    }

    // تحديث مفتاح اللغة
    updateLanguageSelector() {
        document.querySelectorAll('.language-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        const activeBtn = document.querySelector(`.language-btn:nth-child(${this.getLanguageIndex() + 1})`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
    }

    // الحصول على فهرس اللغة
    getLanguageIndex() {
        const languages = ['ar', 'en', 'fr'];
        return languages.indexOf(this.currentLanguage);
    }

    // تهيئة النظام
    init() {
        // تطبيق اللغة المحفوظة
        this.updatePageLanguage();
        
        // إضافة CSS للغات
        this.addLanguageStyles();
    }

    // إضافة أنماط CSS للغات
    addLanguageStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .language-selector-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border-radius: 15px;
                padding: 10px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .language-selector {
                display: flex;
                gap: 8px;
                align-items: center;
            }

            .language-btn {
                background: transparent;
                border: 2px solid transparent;
                border-radius: 10px;
                padding: 8px 12px;
                cursor: pointer;
                font-size: 14px;
                font-weight: 500;
                transition: all 0.3s ease;
                color: #64748b;
                white-space: nowrap;
            }

            .language-btn:hover {
                background: rgba(102, 126, 234, 0.1);
                border-color: rgba(102, 126, 234, 0.3);
                transform: translateY(-2px);
            }

            .language-btn.active {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-color: transparent;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }

            /* تعديل للشاشات الصغيرة */
            @media (max-width: 768px) {
                .language-selector-container {
                    top: 10px;
                    right: 10px;
                    padding: 8px;
                }

                .language-btn {
                    padding: 6px 8px;
                    font-size: 12px;
                }
            }

            /* دعم RTL */
            [dir="rtl"] .language-selector-container {
                right: auto;
                left: 20px;
            }

            [dir="rtl"] .language-selector {
                flex-direction: row-reverse;
            }

            @media (max-width: 768px) {
                [dir="rtl"] .language-selector-container {
                    left: 10px;
                }
            }

            /* دعم شامل لاتجاه النص */
            [dir="ltr"] {
                text-align: left;
            }

            [dir="rtl"] {
                text-align: right;
            }

            /* تحسينات للخطوط حسب اللغة */
            [lang="ar"] {
                font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }

            [lang="en"], [lang="fr"] {
                font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }

            /* تحسينات للقوائم والعناصر */
            [dir="ltr"] .sidebar-menu-item {
                text-align: left;
            }

            [dir="ltr"] .form-group-sidebar {
                text-align: left;
            }

            [dir="ltr"] .auth-form {
                text-align: left;
            }

            [dir="ltr"] .user-info {
                text-align: left;
            }

            /* تحسينات للأزرار */
            [dir="ltr"] .btn, [dir="ltr"] .sidebar-btn {
                text-align: center;
            }

            /* تحسينات للجداول */
            [dir="ltr"] table {
                text-align: left;
            }

            [dir="ltr"] th, [dir="ltr"] td {
                text-align: left;
            }

            /* تحسينات للنماذج */
            [dir="ltr"] input, [dir="ltr"] textarea, [dir="ltr"] select {
                text-align: left;
            }

            [dir="rtl"] input, [dir="rtl"] textarea, [dir="rtl"] select {
                text-align: right;
            }

            /* تحسينات للرسائل والإشعارات */
            [dir="ltr"] .toast {
                text-align: left;
            }

            [dir="ltr"] .modal-content {
                text-align: left;
            }

            /* تحسينات للقوائم المنسدلة */
            [dir="ltr"] .dropdown-menu {
                text-align: left;
            }

            /* تحسينات للبطاقات */
            [dir="ltr"] .card, [dir="ltr"] .service-card {
                text-align: left;
            }

            /* تحسينات للإحصائيات */
            [dir="ltr"] .stat-card, [dir="ltr"] .stat-item {
                text-align: center;
            }
        `;
        document.head.appendChild(style);
    }
}

// تهيئة مدير اللغات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌐 بدء تهيئة نظام إدارة اللغات...');
    try {
        window.languageManager = new LanguageManager();
        window.languageManager.init();
        console.log('✅ تم تهيئة نظام إدارة اللغات بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تهيئة نظام إدارة اللغات:', error);
    }
});

// تهيئة إضافية في حالة تأخر تحميل DOM
if (document.readyState === 'loading') {
    // DOM لم يتم تحميله بعد
    document.addEventListener('DOMContentLoaded', initLanguageManager);
} else {
    // DOM تم تحميله بالفعل
    initLanguageManager();
}

function initLanguageManager() {
    if (!window.languageManager) {
        console.log('🔄 تهيئة مدير اللغات...');
        try {
            window.languageManager = new LanguageManager();
            window.languageManager.init();
            console.log('✅ تم تهيئة مدير اللغات');
        } catch (error) {
            console.error('❌ خطأ في تهيئة مدير اللغات:', error);
        }
    }
}

// دالة مساعدة للترجمة
function t(key) {
    return window.languageManager ? window.languageManager.translate(key) : key;
}
