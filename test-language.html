<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="site_title">اختبار نظام اللغات</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 data-translate="site_title">اختبار نظام اللغات</h1>
        
        <div class="test-section">
            <h2 data-translate="system_status">حالة النظام</h2>
            <div class="test-item">
                <strong data-translate="language">اللغة الحالية:</strong> 
                <span id="currentLang">العربية</span>
                <span class="status success" id="langStatus">نشط</span>
            </div>
            <div class="test-item">
                <strong>مدير اللغات:</strong> 
                <span id="managerStatus">جاري التحقق...</span>
            </div>
            <div class="test-item">
                <strong>مفتاح اللغة:</strong> 
                <span id="selectorStatus">جاري التحقق...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h2 data-translate="main_features">اختبار الترجمات</h2>
            <div class="test-item">
                <p data-translate="welcome">مرحباً بك</p>
            </div>
            <div class="test-item">
                <p data-translate="site_subtitle">نظام شامل لإدارة المراكز الصحية وتتبع التلقيحات والأدوية ووسائل منع الحمل</p>
            </div>
            <div class="test-item">
                <button data-translate="save">حفظ</button>
                <button data-translate="cancel">إلغاء</button>
                <button data-translate="delete">حذف</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>اختبار يدوي</h2>
            <div class="test-item">
                <button onclick="testLanguageChange('ar')">🇸🇦 العربية</button>
                <button onclick="testLanguageChange('en')">🇺🇸 English</button>
                <button onclick="testLanguageChange('fr')">🇫🇷 Français</button>
            </div>
            <div class="test-item">
                <button onclick="checkLanguageManager()">فحص مدير اللغات</button>
                <button onclick="forceCreateSelector()">إنشاء مفتاح اللغة</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>معلومات التشخيص</h2>
            <div class="test-item">
                <pre id="diagnostics">جاري التحميل...</pre>
            </div>
        </div>
    </div>

    <script>
        // فحص حالة النظام
        function checkSystemStatus() {
            // فحص مدير اللغات
            const managerStatus = document.getElementById('managerStatus');
            if (window.languageManager) {
                managerStatus.innerHTML = '<span class="status success">متوفر</span>';
            } else {
                managerStatus.innerHTML = '<span class="status error">غير متوفر</span>';
            }
            
            // فحص مفتاح اللغة
            const selectorStatus = document.getElementById('selectorStatus');
            const selector = document.querySelector('.language-selector-container');
            if (selector) {
                selectorStatus.innerHTML = '<span class="status success">موجود</span>';
            } else {
                selectorStatus.innerHTML = '<span class="status error">غير موجود</span>';
            }
            
            // تحديث معلومات التشخيص
            updateDiagnostics();
        }
        
        function testLanguageChange(lang) {
            if (window.languageManager) {
                window.languageManager.changeLanguage(lang);
                document.getElementById('currentLang').textContent = 
                    lang === 'ar' ? 'العربية' : 
                    lang === 'en' ? 'English' : 'Français';
            } else {
                alert('مدير اللغات غير متوفر');
            }
        }
        
        function checkLanguageManager() {
            console.log('Language Manager:', window.languageManager);
            console.log('Current Language:', window.languageManager?.currentLanguage);
            console.log('Translations:', window.languageManager?.translations);
            updateDiagnostics();
        }
        
        function forceCreateSelector() {
            if (window.languageManager) {
                window.languageManager.initializeLanguageSelector();
                setTimeout(checkSystemStatus, 100);
            } else {
                alert('مدير اللغات غير متوفر');
            }
        }
        
        function updateDiagnostics() {
            const diagnostics = document.getElementById('diagnostics');
            const info = {
                'مدير اللغات': !!window.languageManager,
                'اللغة الحالية': window.languageManager?.currentLanguage || 'غير محدد',
                'مفتاح اللغة': !!document.querySelector('.language-selector-container'),
                'عدد الترجمات': Object.keys(window.languageManager?.translations?.ar || {}).length,
                'حالة DOM': document.readyState,
                'وقت التحميل': new Date().toLocaleTimeString()
            };
            
            diagnostics.textContent = JSON.stringify(info, null, 2);
        }
        
        // فحص دوري
        setInterval(checkSystemStatus, 2000);
        
        // فحص أولي
        setTimeout(checkSystemStatus, 500);
    </script>
    
    <!-- تحميل نظام إدارة اللغات -->
    <script src="js/language-manager.js"></script>
</body>
</html>
