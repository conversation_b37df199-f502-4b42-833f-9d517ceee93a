<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate="site_title">اختبار الترجمة</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            transition: all 0.3s ease;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .language-selector-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 10px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .language-selector {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .language-btn {
            background: transparent;
            border: 2px solid transparent;
            border-radius: 10px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            color: #64748b;
            white-space: nowrap;
        }

        .language-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: rgba(102, 126, 234, 0.3);
            transform: translateY(-2px);
        }

        .language-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background: #5a6fd8;
        }
        
        /* دعم RTL */
        [dir="rtl"] .language-selector-container {
            right: auto;
            left: 20px;
        }

        [dir="rtl"] .language-selector {
            flex-direction: row-reverse;
        }
        
        [dir="ltr"] {
            text-align: left;
        }

        [dir="rtl"] {
            text-align: right;
        }
        
        [lang="ar"] {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        [lang="en"], [lang="fr"] {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
    </style>
</head>
<body>
    <!-- مفتاح تبديل اللغة -->
    <div class="language-selector-container">
        <div class="language-selector">
            <button class="language-btn active" onclick="changeLanguage('ar')">🇸🇦 العربية</button>
            <button class="language-btn" onclick="changeLanguage('en')">🇺🇸 English</button>
            <button class="language-btn" onclick="changeLanguage('fr')">🇫🇷 Français</button>
        </div>
    </div>

    <div class="container">
        <h1 data-translate="site_title">اختبار الترجمة</h1>
        
        <div class="test-section">
            <h2 data-translate="system_status">حالة النظام</h2>
            <div class="test-item">
                <p data-translate="site_subtitle">نظام شامل لإدارة المراكز الصحية وتتبع التلقيحات والأدوية ووسائل منع الحمل</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2 data-translate="main_features">المميزات الرئيسية</h2>
            <div class="test-item">
                <p data-translate="children_management">إدارة شاملة للأطفال والتلقيحات</p>
            </div>
            <div class="test-item">
                <p data-translate="vaccine_tracking">تتبع اللقاحات والمخزون</p>
            </div>
            <div class="test-item">
                <p data-translate="medicine_management">إدارة الأدوية ووسائل منع الحمل</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>أزرار الاختبار</h2>
            <div class="test-item">
                <button data-translate="install_system">تثبيت النظام</button>
                <button data-translate="system_test">اختبار النظام</button>
                <button data-translate="user_guide">دليل المستخدم</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>معلومات</h2>
            <div class="test-item">
                <p><strong data-translate="version">الإصدار:</strong> 2.0.0</p>
                <p><strong data-translate="developer">المطور:</strong> <span data-translate="development_team">فريق تطوير الأنظمة الصحية</span></p>
            </div>
        </div>
    </div>

    <script>
        // دالة تغيير اللغة
        function changeLanguage(lang) {
            console.log('🌐 تغيير اللغة إلى:', lang);
            
            // حفظ اللغة المختارة
            localStorage.setItem('selectedLanguage', lang);
            
            // تحديث اتجاه النص
            const html = document.documentElement;
            const body = document.body;
            
            if (lang === 'ar') {
                html.setAttribute('lang', 'ar');
                html.setAttribute('dir', 'rtl');
                body.style.direction = 'rtl';
            } else {
                html.setAttribute('lang', lang);
                html.setAttribute('dir', 'ltr');
                body.style.direction = 'ltr';
            }
            
            // تحديث الأزرار
            document.querySelectorAll('.language-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // تفعيل الزر المختار
            if (event && event.target) {
                event.target.classList.add('active');
            }
            
            // ترجمات
            const translations = {
                ar: {
                    'site_title': 'اختبار الترجمة',
                    'site_subtitle': 'نظام شامل لإدارة المراكز الصحية وتتبع التلقيحات والأدوية ووسائل منع الحمل',
                    'system_status': 'حالة النظام',
                    'main_features': 'المميزات الرئيسية',
                    'children_management': 'إدارة شاملة للأطفال والتلقيحات',
                    'vaccine_tracking': 'تتبع اللقاحات والمخزون',
                    'medicine_management': 'إدارة الأدوية ووسائل منع الحمل',
                    'install_system': 'تثبيت النظام',
                    'system_test': 'اختبار النظام',
                    'user_guide': 'دليل المستخدم',
                    'version': 'الإصدار',
                    'developer': 'المطور',
                    'development_team': 'فريق تطوير الأنظمة الصحية'
                },
                en: {
                    'site_title': 'Translation Test',
                    'site_subtitle': 'Comprehensive system for managing healthcare centers, tracking vaccinations, medicines, and contraceptives',
                    'system_status': 'System Status',
                    'main_features': 'Main Features',
                    'children_management': 'Comprehensive children and vaccination management',
                    'vaccine_tracking': 'Vaccine and inventory tracking',
                    'medicine_management': 'Medicine and contraceptive management',
                    'install_system': 'Install System',
                    'system_test': 'System Test',
                    'user_guide': 'User Guide',
                    'version': 'Version',
                    'developer': 'Developer',
                    'development_team': 'Healthcare Systems Development Team'
                },
                fr: {
                    'site_title': 'Test de Traduction',
                    'site_subtitle': 'Système complet pour la gestion des centres de santé, le suivi des vaccinations, des médicaments et des contraceptifs',
                    'system_status': 'État du Système',
                    'main_features': 'Fonctionnalités Principales',
                    'children_management': 'Gestion complète des enfants et vaccinations',
                    'vaccine_tracking': 'Suivi des vaccins et inventaire',
                    'medicine_management': 'Gestion des médicaments et contraceptifs',
                    'install_system': 'Installer le Système',
                    'system_test': 'Test du Système',
                    'user_guide': 'Guide Utilisateur',
                    'version': 'Version',
                    'developer': 'Développeur',
                    'development_team': 'Équipe de Développement des Systèmes de Santé'
                }
            };
            
            // تطبيق الترجمات
            console.log('🔄 تطبيق الترجمات...');
            let translatedCount = 0;
            
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (translations[lang] && translations[lang][key]) {
                    element.textContent = translations[lang][key];
                    translatedCount++;
                    console.log(`✅ ترجم: ${key} -> ${translations[lang][key]}`);
                } else {
                    console.log(`❌ لم يجد ترجمة لـ: ${key}`);
                }
            });
            
            console.log(`📊 تم ترجمة ${translatedCount} عنصر`);
            
            // تحديث العنوان
            if (translations[lang] && translations[lang]['site_title']) {
                document.title = translations[lang]['site_title'];
            }
            
            console.log('🎉 تم تغيير اللغة بنجاح');
        }
        
        // تطبيق اللغة المحفوظة عند التحميل
        document.addEventListener('DOMContentLoaded', function() {
            const savedLang = localStorage.getItem('selectedLanguage') || 'ar';
            console.log('💾 اللغة المحفوظة:', savedLang);
            
            setTimeout(() => {
                const fakeEvent = {
                    target: document.querySelector(`[onclick="changeLanguage('${savedLang}')"]`)
                };
                window.event = fakeEvent;
                changeLanguage(savedLang);
            }, 100);
        });
    </script>
</body>
</html>
