{"common": {"language": "Language", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "close": "Close", "loading": "Loading...", "no_data": "No data", "error": "Error", "success": "Success", "confirm": "Confirm", "yes": "Yes", "no": "No", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "print": "Print", "refresh": "Refresh"}, "main_page": {"site_title": "Healthcare Centers Management System", "site_subtitle": "Comprehensive system for managing healthcare centers, tracking vaccinations, medicines, and contraceptives", "system_status": "System Status", "installation_status": "Installation Status", "database_status": "Database", "php_version": "PHP Version", "installed": "Installed", "not_installed": "Not Installed", "connected": "Connected", "not_connected": "Not Connected", "main_features": "Main Features", "children_management": "Comprehensive children and vaccination management", "vaccine_tracking": "Vaccine and inventory tracking", "medicine_management": "Medicine and contraceptive management", "reports_statistics": "Advanced reports and statistics", "user_permissions": "Multi-level permissions system", "mobile_compatible": "Compatible with all devices", "install_system": "Install System", "new_version_api": "New Version (API)", "old_version": "Old Version", "system_test": "System Test", "user_guide": "User Guide", "version": "Version", "update_date": "Update Date", "developer": "Developer", "development_team": "Healthcare Systems Development Team", "note": "Note", "system_ready": "System is ready for use. You can now access the new version that uses MySQL database.", "warning": "Warning", "install_first": "System must be installed first before use. Click \"Install System\" to begin."}, "healthcare_management": {"welcome": "Welcome", "nurse": "Nurse", "center": "Center", "region": "Region", "logout": "Logout", "username": "Username", "password": "Password", "login": "<PERSON><PERSON>", "register": "Create Account", "nurse_name": "Nurse Name", "center_name": "Healthcare Center Name", "account_type": "Account Type", "home_page": "Home Page", "vaccination_calculator": "Vaccination Calculator", "vaccine_management": "Vaccine Management", "medicine_management": "Medicine Management", "family_planning": "Family Planning", "children_registry": "Children Registry", "tasks_list": "Tasks List", "messages": "Messages", "account_management": "Account Management", "notifications": "Notifications", "calculator": "Calculator", "new_message": "New Message", "recipient": "Recipient", "message": "Message", "attachments": "Attachments", "send": "Send"}, "vaccination": {"child_name": "Child Name", "birth_date": "Birth Date", "vaccination_schedule": "Vaccination Schedule", "next_vaccination": "Next Vaccination", "vaccination_history": "Vaccination History", "vaccine_name": "Vaccine Name", "vaccination_date": "Vaccination Date", "due_date": "Due Date", "status": "Status", "completed": "Completed", "pending": "Pending", "overdue": "Overdue", "bcg": "BCG Vaccine", "hepatitis_b": "Hepatitis B", "polio": "Polio", "dtp": "DTP (Diph<PERSON>ia, Tetanus, Pertussis)", "measles": "<PERSON><PERSON><PERSON>", "mmr": "MMR (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)"}, "medicines": {"medicine_name": "Medicine Name", "quantity": "Quantity", "expiry_date": "Expiry Date", "batch_number": "Batch Number", "supplier": "Supplier", "stock_level": "Stock Level", "low_stock": "Low Stock", "out_of_stock": "Out of Stock", "add_medicine": "Add Medicine", "update_stock": "Update Stock", "medicine_category": "Medicine Category", "dosage": "Dosage", "instructions": "Instructions"}, "family_planning": {"contraceptive_method": "Contraceptive Method", "client_name": "Client Name", "age": "Age", "method_type": "Method Type", "start_date": "Start Date", "next_visit": "Next Visit", "side_effects": "Side Effects", "consultation": "Consultation", "follow_up": "Follow-up", "pills": "Birth Control Pills", "injection": "Injection", "iud": "IUD", "implant": "Implant", "condoms": "Condoms"}}